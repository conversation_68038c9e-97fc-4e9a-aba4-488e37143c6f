# HarmoniHSE360 Local Development Prometheus Configuration
# Optimized for local development environment monitoring

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    environment: 'local-development'
    project: 'harmoni-hse-360'

# Alertmanager configuration (optional for local development)
# alerting:
#   alertmanagers:
#     - static_configs:
#         - targets:
#           - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
  - "/etc/prometheus/rules/*.yml"

# Scrape configuration
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # HarmoniHSE360 Application Metrics
  - job_name: 'harmoni360-app'
    static_configs:
      - targets: ['app:8080']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: app:8080

  # PostgreSQL Database Metrics (via postgres_exporter if available)
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s
    # Note: Requires postgres_exporter sidecar container

  # Redis Metrics (via redis_exporter if available)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s
    # Note: Requires redis_exporter sidecar container

  # Node Exporter (System Metrics)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s

  # Nginx Metrics (if nginx-prometheus-exporter is available)
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:9113']
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s
    # Note: Requires nginx-prometheus-exporter sidecar container

  # Docker Container Metrics (cAdvisor)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s
    # Note: Requires cAdvisor container

  # Grafana Metrics
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    scrape_interval: 60s
    metrics_path: /metrics
    scrape_timeout: 10s

# Storage configuration for local development
storage:
  tsdb:
    path: /prometheus
    retention.time: 30d
    retention.size: 10GB
    wal-compression: true

# Remote write configuration (optional - for sending metrics to external systems)
# remote_write:
#   - url: "https://your-remote-prometheus-endpoint/api/v1/write"
#     basic_auth:
#       username: "your-username"
#       password: "your-password"

# Remote read configuration (optional)
# remote_read:
#   - url: "https://your-remote-prometheus-endpoint/api/v1/read"
#     basic_auth:
#       username: "your-username"
#       password: "your-password"

# Recording rules for common queries
recording_rules:
  - name: harmoni360_application_rules
    interval: 30s
    rules:
      # HTTP request rate
      - record: harmoni360:http_requests:rate5m
        expr: rate(http_requests_total[5m])
        labels:
          job: harmoni360-app

      # HTTP error rate
      - record: harmoni360:http_errors:rate5m
        expr: rate(http_requests_total{status=~"5.."}[5m])
        labels:
          job: harmoni360-app

      # Database connection pool usage
      - record: harmoni360:db_connections:usage
        expr: db_connections_active / db_connections_max
        labels:
          job: harmoni360-app

      # Memory usage percentage
      - record: harmoni360:memory:usage_percent
        expr: (process_resident_memory_bytes / node_memory_MemTotal_bytes) * 100
        labels:
          job: harmoni360-app

  - name: system_rules
    interval: 30s
    rules:
      # CPU usage
      - record: node:cpu_utilization:rate5m
        expr: 1 - rate(node_cpu_seconds_total{mode="idle"}[5m])

      # Memory usage
      - record: node:memory_utilization:ratio
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes

      # Disk usage
      - record: node:disk_utilization:ratio
        expr: (node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes

      # Network I/O
      - record: node:network_receive:rate5m
        expr: rate(node_network_receive_bytes_total[5m])

      - record: node:network_transmit:rate5m
        expr: rate(node_network_transmit_bytes_total[5m])

# Alerting rules for local development
alerting_rules:
  - name: harmoni360_alerts
    rules:
      # Application is down
      - alert: ApplicationDown
        expr: up{job="harmoni360-app"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "HarmoniHSE360 application is down"
          description: "The HarmoniHSE360 application has been down for more than 1 minute."

      # High error rate
      - alert: HighErrorRate
        expr: harmoni360:http_errors:rate5m > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High HTTP error rate detected"
          description: "HTTP error rate is {{ $value }} errors per second."

      # High memory usage
      - alert: HighMemoryUsage
        expr: harmoni360:memory:usage_percent > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value }}%."

      # Database connection issues
      - alert: DatabaseConnectionHigh
        expr: harmoni360:db_connections:usage > 0.8
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High database connection usage"
          description: "Database connection pool usage is {{ $value }}."

  - name: system_alerts
    rules:
      # High CPU usage
      - alert: HighCPUUsage
        expr: node:cpu_utilization:rate5m > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value }}."

      # High memory usage
      - alert: HighSystemMemoryUsage
        expr: node:memory_utilization:ratio > 0.9
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High system memory usage"
          description: "System memory usage is {{ $value }}."

      # Low disk space
      - alert: LowDiskSpace
        expr: node:disk_utilization:ratio > 0.9
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Low disk space"
          description: "Disk usage is {{ $value }}."

      # Node exporter down
      - alert: NodeExporterDown
        expr: up{job="node-exporter"} == 0
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Node Exporter is down"
          description: "Node Exporter has been down for more than 1 minute."
