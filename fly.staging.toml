# Fly.io configuration file for Harmoni360 Staging Environment

app = "harmoni360-staging"
primary_region = "sjc"

[build]
  dockerfile = "Dockerfile.flyio"

[env]
  ASPNETCORE_ENVIRONMENT = "Staging"
  ASPNETCORE_URLS = "http://+:8080"
  ASPNETCORE_FORWARDEDHEADERS_ENABLED = "true"

[[services]]
  internal_port = 8080
  protocol = "tcp"
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0

  [[services.ports]]
    handlers = ["http"]
    port = 80
    force_https = true

  [[services.ports]]
    handlers = ["tls", "http"]
    port = 443

  [services.concurrency]
    type = "connections"
    hard_limit = 25
    soft_limit = 20

[[services.http_checks]]
  interval = "15s"
  grace_period = "10s"
  method = "GET"
  path = "/health"
  protocol = "http"
  timeout = "5s"
  tls_skip_verify = false

[mounts]
  source = "harmoni360_staging_uploads"
  destination = "/app/uploads"

# Staging-specific machine configuration
[vm]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 512

# Restart policy for staging stability
[restart]
  policy = "on-failure"
