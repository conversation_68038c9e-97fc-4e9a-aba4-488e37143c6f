# Grafana Dashboard Provisioning Configuration
# Automatically loads dashboards from the dashboards directory

apiVersion: 1

providers:
  # HarmoniHSE360 Application Dashboards
  - name: 'harmoni360-dashboards'
    orgId: 1
    folder: 'HarmoniHSE360'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/harmoni360

  # System Monitoring Dashboards
  - name: 'system-dashboards'
    orgId: 1
    folder: 'System Monitoring'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/system

  # Infrastructure Dashboards
  - name: 'infrastructure-dashboards'
    orgId: 1
    folder: 'Infrastructure'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/infrastructure
