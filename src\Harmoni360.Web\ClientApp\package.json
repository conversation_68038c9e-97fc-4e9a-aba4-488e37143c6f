{"name": "harmoni360-client", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc --noEmit || true && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives || true", "preview": "vite preview", "format:check": "npx prettier --check src/**/*.{ts,tsx}", "format": "prettier --write src/**/*.{ts,tsx}", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch"}, "dependencies": {"@coreui/coreui": "^5.0.0", "@coreui/icons": "^3.0.1", "@coreui/icons-react": "^2.2.1", "@coreui/react": "^5.0.0", "@coreui/react-chartjs": "^3.0.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@hookform/resolvers": "^5.1.1", "@microsoft/signalr": "^8.0.0", "@reduxjs/toolkit": "^2.0.1", "@tanstack/react-query": "^5.80.6", "@types/file-saver": "^2.0.7", "@types/jspdf": "^1.3.3", "axios": "^1.6.5", "chart.js": "^4.4.9", "date-fns": "^3.2.0", "file-saver": "^2.0.5", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-redux": "^9.1.0", "react-router-dom": "^7.6.2", "react-toastify": "^11.0.5", "react-window": "^1.8.11", "xlsx": "^0.18.5", "yup": "^1.3.3"}, "devDependencies": {"@types/node": "^24.0.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/react-window": "^1.8.8", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "@vitejs/plugin-react": "^4.2.1", "eslint": "^9.28.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.5", "prettier": "^3.0.0", "sass": "^1.69.7", "typescript": "^5.2.2", "vite": "^6.3.5", "vite-plugin-pwa": "^1.0.0"}}