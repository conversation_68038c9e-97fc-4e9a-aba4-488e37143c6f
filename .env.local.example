# HarmoniHSE360 Local Development Environment Configuration
# Copy this file to .env.local and customize the values for your local development environment
# This configuration simulates the production Docker Standalone deployment

# =============================================================================
# COMPOSE CONFIGURATION
# =============================================================================
COMPOSE_PROJECT_NAME=harmoni360-local
COMPOSE_FILE=docker-compose.local.yml

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
ASPNETCORE_ENVIRONMENT=Development
APP_PORT=8080
APP_BASE_URL=https://localhost
BUILD_VERSION=latest
DEMO_MODE=true
FORCE_RESEED=false

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
POSTGRES_DB=Harmoni360_Local
POSTGRES_USER=harmoni360
POSTGRES_PASSWORD=LocalDev_SecurePassword123!
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# PostgreSQL Performance Tuning (Local Development)
POSTGRES_SHARED_BUFFERS=256MB
POSTGRES_EFFECTIVE_CACHE_SIZE=1GB
POSTGRES_WORK_MEM=16MB
POSTGRES_MAINTENANCE_WORK_MEM=128MB
POSTGRES_MAX_CONNECTIONS=100

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_PASSWORD=LocalDev_RedisPassword456!
REDIS_PORT=6379

# =============================================================================
# JWT CONFIGURATION
# =============================================================================
# Generate a secure JWT key (32+ characters)
# You can generate one using: openssl rand -base64 32
JWT_KEY=LocalDev_JwtSecretKeyThatMustBeAtLeast32CharactersLongForSecurity!
JWT_ISSUER=Harmoni360
JWT_AUDIENCE=Harmoni360Users
JWT_EXPIRATION_MINUTES=60
JWT_REFRESH_TOKEN_EXPIRATION_DAYS=7

# =============================================================================
# NGINX CONFIGURATION
# =============================================================================
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================

# Prometheus Configuration
PROMETHEUS_PORT=9090

# Grafana Configuration
GRAFANA_PORT=3000
GRAFANA_ADMIN_USER=admin
# Generate a secure Grafana password
GRAFANA_ADMIN_PASSWORD=LocalDev_GrafanaAdmin789!

# Seq Logging Configuration
SEQ_PORT=5341
# Generate Seq password hash using: echo 'your-password' | docker run --rm -i datalust/seq config hash
SEQ_ADMIN_PASSWORD_HASH=$SHA256$V1$10000$Hashed_Password_Here
SEQ_API_KEY=

# Node Exporter Configuration
NODE_EXPORTER_PORT=9100

# =============================================================================
# NGROK CONFIGURATION
# =============================================================================
# These values are used by scripts to configure ngrok tunneling
NGROK_AUTHTOKEN=your_ngrok_authtoken_here
NGROK_REGION=us
NGROK_SUBDOMAIN_APP=harmoni360-dev
NGROK_SUBDOMAIN_GRAFANA=harmoni360-grafana
NGROK_SUBDOMAIN_PROMETHEUS=harmoni360-prometheus
NGROK_SUBDOMAIN_SEQ=harmoni360-seq

# =============================================================================
# SSL CONFIGURATION
# =============================================================================
# SSL certificate paths (for self-signed certificates in local development)
SSL_CERT_PATH=./local-dev/ssl/cert.pem
SSL_KEY_PATH=./local-dev/ssl/key.pem
SSL_DOMAIN=localhost

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
BACKUP_RETENTION_DAYS=7
BACKUP_SCHEDULE=0 2 * * *
BACKUP_ENCRYPTION_KEY=LocalDev_BackupEncryptionKey!

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
# Enable development features
ENABLE_SWAGGER=true
ENABLE_DETAILED_ERRORS=true
ENABLE_SENSITIVE_DATA_LOGGING=false
ENABLE_HOT_RELOAD=true

# Development database seeding
SEED_SAMPLE_DATA=true
SEED_TEST_USERS=true
SEED_DEMO_INCIDENTS=true

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=Information
LOG_FILE_PATH=./local-dev/logs/application.log
LOG_RETENTION_DAYS=30

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
# Resource limits for local development
MAX_REQUEST_SIZE=100MB
CONNECTION_TIMEOUT=30
COMMAND_TIMEOUT=30

# Cache configuration
CACHE_DEFAULT_EXPIRATION=3600
CACHE_SLIDING_EXPIRATION=1800

# =============================================================================
# SECURITY CONFIGURATION (DEVELOPMENT ONLY)
# =============================================================================
# WARNING: These are development-only settings. Never use in production!
ALLOW_INSECURE_HTTP=true
DISABLE_HTTPS_REDIRECTION=false
CORS_ALLOW_ANY_ORIGIN=true
TRUST_PROXY_HEADERS=true

# =============================================================================
# FEATURE FLAGS
# =============================================================================
FEATURE_ENABLE_REAL_TIME_NOTIFICATIONS=true
FEATURE_ENABLE_FILE_UPLOADS=true
FEATURE_ENABLE_AUDIT_LOGGING=true
FEATURE_ENABLE_PERFORMANCE_MONITORING=true

# =============================================================================
# EXTERNAL SERVICES (OPTIONAL)
# =============================================================================
# Email service configuration (for testing)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM_ADDRESS=<EMAIL>
SMTP_FROM_NAME=HarmoniHSE360 Local

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================
# Hot reload configuration
DOTNET_WATCH_RESTART_ON_RUDE_EDIT=true
DOTNET_WATCH_SUPPRESS_LAUNCH_BROWSER=true

# Entity Framework configuration
EF_ADD_MIGRATION_STARTUP_PROJECT=src/Harmoni360.Web
EF_MIGRATION_PROJECT=src/Harmoni360.Infrastructure

# =============================================================================
# WINDOWS-SPECIFIC CONFIGURATION
# =============================================================================
# Docker Desktop resource allocation
DOCKER_MEMORY_LIMIT=20g
DOCKER_CPU_LIMIT=6
DOCKER_SWAP_LIMIT=4g

# Windows file sharing
DOCKER_SHARED_DRIVES=C

# =============================================================================
# TROUBLESHOOTING
# =============================================================================
# Enable verbose logging for troubleshooting
VERBOSE_LOGGING=false
DEBUG_MODE=false
TRACE_REQUESTS=false

# Health check configuration
HEALTH_CHECK_TIMEOUT=30
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_RETRIES=5

# =============================================================================
# NOTES
# =============================================================================
# 1. Copy this file to .env.local and customize the values
# 2. Never commit .env.local to version control
# 3. Generate secure passwords for all services
# 4. Update NGROK_AUTHTOKEN with your actual ngrok token
# 5. For Seq password hash, use: echo 'your-password' | docker run --rm -i datalust/seq config hash
# 6. Ensure all ports are available on your local machine
# 7. Adjust resource limits based on your system capabilities

# =============================================================================
# QUICK SETUP COMMANDS
# =============================================================================
# 1. Generate JWT key: openssl rand -base64 32
# 2. Generate passwords: openssl rand -base64 16
# 3. Generate Seq hash: echo 'password' | docker run --rm -i datalust/seq config hash
# 4. Start environment: docker-compose -f docker-compose.local.yml up -d
# 5. Check status: docker-compose -f docker-compose.local.yml ps
# 6. View logs: docker-compose -f docker-compose.local.yml logs -f
# 7. Stop environment: docker-compose -f docker-compose.local.yml down
