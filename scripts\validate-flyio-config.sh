#!/bin/bash

# Harmoni360 Fly.io Configuration Validation Script
# This script validates that all configuration fixes are in place

set -e

echo "🔍 Validating Harmoni360 Fly.io Configuration..."
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check if a file exists and contains expected content
check_file_content() {
    local file="$1"
    local pattern="$2"
    local description="$3"
    
    if [ -f "$file" ]; then
        if grep -q "$pattern" "$file"; then
            echo -e "✅ ${GREEN}$description${NC}"
            return 0
        else
            echo -e "❌ ${RED}$description - Pattern not found: $pattern${NC}"
            return 1
        fi
    else
        echo -e "❌ ${RED}$description - File not found: $file${NC}"
        return 1
    fi
}

# Function to check if a file exists
check_file_exists() {
    local file="$1"
    local description="$2"
    
    if [ -f "$file" ]; then
        echo -e "✅ ${GREEN}$description${NC}"
        return 0
    else
        echo -e "❌ ${RED}$description - File not found: $file${NC}"
        return 1
    fi
}

echo "📋 Checking Configuration Files..."
echo "-----------------------------------"

# Check if production fly.toml exists
check_file_exists "fly.toml" "Production fly.toml exists"

# Check if staging fly.staging.toml exists
check_file_exists "fly.staging.toml" "Staging fly.staging.toml exists"

# Check app names
check_file_content "fly.toml" "app = \"harmoni-hse-360\"" "Production fly.toml has correct app name"
check_file_content "fly.staging.toml" "app = \"harmoni360-staging\"" "Staging fly.staging.toml has correct app name"

# Check Dockerfile.flyio uses correct .NET version
check_file_content "Dockerfile.flyio" "mcr.microsoft.com/dotnet/sdk:8.0-alpine" "Dockerfile.flyio uses .NET 8.0 SDK"
check_file_content "Dockerfile.flyio" "mcr.microsoft.com/dotnet/aspnet:8.0-alpine" "Dockerfile.flyio uses .NET 8.0 runtime"

# Check project targets .NET 8.0
check_file_content "src/Harmoni360.Web/Harmoni360.Web.csproj" "<TargetFramework>net8.0</TargetFramework>" "Project targets .NET 8.0"

# Check GitHub Actions workflow uses correct health check URLs
check_file_content ".github/workflows/deploy.yml" "harmoni-hse-360.fly.dev/health" "GitHub Actions uses correct production health check URL"
check_file_content ".github/workflows/deploy.yml" "harmoni360-staging.fly.dev/health" "GitHub Actions uses correct staging health check URL"

# Check Program.cs has conditional HTTPS redirection
check_file_content "src/Harmoni360.Web/Program.cs" "if (app.Environment.IsDevelopment())" "Program.cs has conditional HTTPS redirection"

# Check Program.cs has startup logging
check_file_content "src/Harmoni360.Web/Program.cs" "Starting Harmoni360 application" "Program.cs has startup logging"

echo ""
echo "🔧 Checking Application Configuration..."
echo "---------------------------------------"

# Check if health checks are configured
check_file_content "src/Harmoni360.Web/Program.cs" "app.MapHealthChecks(\"/health\")" "Health checks endpoint is mapped"

# Check if ASPNETCORE_URLS is set in both configurations
check_file_content "fly.toml" "ASPNETCORE_URLS = \"http://+:8080\"" "Production ASPNETCORE_URLS configured correctly"
check_file_content "fly.staging.toml" "ASPNETCORE_URLS = \"http://+:8080\"" "Staging ASPNETCORE_URLS configured correctly"

# Check internal port configuration
check_file_content "fly.toml" "internal_port = 8080" "Production internal port configured correctly"
check_file_content "fly.staging.toml" "internal_port = 8080" "Staging internal port configured correctly"

# Check forwarded headers configuration
check_file_content "fly.toml" "ASPNETCORE_FORWARDEDHEADERS_ENABLED = \"true\"" "Production forwarded headers enabled"
check_file_content "fly.staging.toml" "ASPNETCORE_FORWARDEDHEADERS_ENABLED = \"true\"" "Staging forwarded headers enabled"

echo ""
echo "🚀 Deployment Readiness Check..."
echo "--------------------------------"

# Check if fly CLI is available
if command -v flyctl &> /dev/null; then
    echo -e "✅ ${GREEN}Fly CLI is installed${NC}"
    
    # Check if authenticated
    if flyctl auth whoami &> /dev/null; then
        echo -e "✅ ${GREEN}Authenticated with Fly.io${NC}"
    else
        echo -e "⚠️  ${YELLOW}Not authenticated with Fly.io - run 'flyctl auth login'${NC}"
    fi
else
    echo -e "❌ ${RED}Fly CLI not installed - install from https://fly.io/docs/hands-on/install-flyctl/${NC}"
fi

echo ""
echo "📊 Validation Summary"
echo "===================="

# Count total checks and passed checks
total_checks=18
passed_checks=0

# Re-run checks silently to count passes
check_file_exists "fly.toml" "" && ((passed_checks++)) || true
check_file_exists "fly.staging.toml" "" && ((passed_checks++)) || true
check_file_content "fly.toml" "app = \"harmoni-hse-360\"" "" && ((passed_checks++)) || true
check_file_content "fly.staging.toml" "app = \"harmoni360-staging\"" "" && ((passed_checks++)) || true
check_file_content "Dockerfile.flyio" "mcr.microsoft.com/dotnet/sdk:8.0-alpine" "" && ((passed_checks++)) || true
check_file_content "Dockerfile.flyio" "mcr.microsoft.com/dotnet/aspnet:8.0-alpine" "" && ((passed_checks++)) || true
check_file_content "src/Harmoni360.Web/Harmoni360.Web.csproj" "<TargetFramework>net8.0</TargetFramework>" "" && ((passed_checks++)) || true
check_file_content ".github/workflows/deploy.yml" "harmoni-hse-360.fly.dev/health" "" && ((passed_checks++)) || true
check_file_content ".github/workflows/deploy.yml" "harmoni360-staging.fly.dev/health" "" && ((passed_checks++)) || true
check_file_content "src/Harmoni360.Web/Program.cs" "if (app.Environment.IsDevelopment())" "" && ((passed_checks++)) || true
check_file_content "src/Harmoni360.Web/Program.cs" "Starting Harmoni360 application" "" && ((passed_checks++)) || true
check_file_content "src/Harmoni360.Web/Program.cs" "app.MapHealthChecks(\"/health\")" "" && ((passed_checks++)) || true
check_file_content "fly.toml" "ASPNETCORE_URLS = \"http://+:8080\"" "" && ((passed_checks++)) || true
check_file_content "fly.staging.toml" "ASPNETCORE_URLS = \"http://+:8080\"" "" && ((passed_checks++)) || true
check_file_content "fly.toml" "internal_port = 8080" "" && ((passed_checks++)) || true
check_file_content "fly.staging.toml" "internal_port = 8080" "" && ((passed_checks++)) || true
check_file_content "fly.toml" "ASPNETCORE_FORWARDEDHEADERS_ENABLED = \"true\"" "" && ((passed_checks++)) || true
check_file_content "fly.staging.toml" "ASPNETCORE_FORWARDEDHEADERS_ENABLED = \"true\"" "" && ((passed_checks++)) || true

echo "Passed: $passed_checks/$total_checks checks"

if [ $passed_checks -eq $total_checks ]; then
    echo -e "🎉 ${GREEN}All configuration checks passed! Ready for deployment.${NC}"
    echo ""
    echo "Next steps:"
    echo "1. Commit and push changes to repository"
    echo "2. Deploy staging: flyctl deploy --config fly.staging.toml"
    echo "3. Deploy production: flyctl deploy --config fly.toml"
    echo "4. Monitor staging: flyctl logs -f -a harmoni360-staging"
    echo "5. Monitor production: flyctl logs -f -a harmoni-hse-360"
    echo "6. Test staging health: curl https://harmoni360-staging.fly.dev/health"
    echo "7. Test production health: curl https://harmoni-hse-360.fly.dev/health"
    exit 0
else
    echo -e "⚠️  ${YELLOW}Some configuration issues found. Please fix them before deployment.${NC}"
    exit 1
fi
