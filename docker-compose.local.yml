# HarmoniHSE360 Local Development Docker Compose
# Simulates production Docker Standalone deployment on Windows 11
# For use with ngrok tunneling and local development

version: '3.8'

networks:
  harmoni360-frontend:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  harmoni360-backend:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  harmoni360-monitoring:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  seq_data:
    driver: local
  uploads_data:
    driver: local

services:
  # PostgreSQL Database with Production Configuration
  postgres:
    image: postgres:15-alpine
    container_name: harmoni360-db-local
    hostname: postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-Harmoni360_Local}
      POSTGRES_USER: ${POSTGRES_USER:-harmoni360}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=en_US.UTF-8"
      # Performance tuning for local development
      POSTGRES_SHARED_BUFFERS: ${POSTGRES_SHARED_BUFFERS:-256MB}
      POSTGRES_EFFECTIVE_CACHE_SIZE: ${POSTGRES_EFFECTIVE_CACHE_SIZE:-1GB}
      POSTGRES_WORK_MEM: ${POSTGRES_WORK_MEM:-16MB}
      POSTGRES_MAINTENANCE_WORK_MEM: ${POSTGRES_MAINTENANCE_WORK_MEM:-128MB}
      POSTGRES_MAX_CONNECTIONS: ${POSTGRES_MAX_CONNECTIONS:-100}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./local-dev/backups:/backups
      - ./local-dev/config/postgres/init-scripts:/docker-entrypoint-initdb.d/
    networks:
      - harmoni360-backend
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-harmoni360} -d ${POSTGRES_DB:-Harmoni360_Local}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '1.0'
        reservations:
          memory: 2G
          cpus: '0.5'
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"

  # Redis Cache with Persistence
  redis:
    image: redis:7-alpine
    container_name: harmoni360-cache-local
    hostname: redis
    command: >
      redis-server
      --requirepass ${REDIS_PASSWORD}
      --appendonly yes
      --appendfsync everysec
      --save 900 1
      --save 300 10
      --save 60 10000
      --maxmemory 2gb
      --maxmemory-policy allkeys-lru
      --tcp-keepalive 60
      --timeout 300
    volumes:
      - redis_data:/data
      - ./local-dev/config/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - harmoni360-backend
    ports:
      - "${REDIS_PORT:-6379}:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"

  # HarmoniHSE360 Web Application
  app:
    build:
      context: .
      dockerfile: Dockerfile.flyio
      args:
        BUILDKIT_INLINE_CACHE: 1
    image: harmoni360:local-dev-${BUILD_VERSION:-latest}
    container_name: harmoni360-app-local
    hostname: app
    environment:
      - ASPNETCORE_ENVIRONMENT=${ASPNETCORE_ENVIRONMENT:-Development}
      - ASPNETCORE_URLS=http://+:8080
      - ASPNETCORE_FORWARDEDHEADERS_ENABLED=true
      # Database Configuration
      - ConnectionStrings__DefaultConnection=Host=postgres;Port=5432;Database=${POSTGRES_DB:-Harmoni360_Local};Username=${POSTGRES_USER:-harmoni360};Password=${POSTGRES_PASSWORD};Pooling=true;MinPoolSize=10;MaxPoolSize=100;CommandTimeout=30;
      - ConnectionStrings__Redis=redis:6379,password=${REDIS_PASSWORD},connectTimeout=5000,syncTimeout=5000,responseTimeout=5000
      # JWT Configuration
      - Jwt__Key=${JWT_KEY}
      - Jwt__Issuer=${JWT_ISSUER:-Harmoni360}
      - Jwt__Audience=${JWT_AUDIENCE:-Harmoni360Users}
      - Jwt__ExpirationMinutes=${JWT_EXPIRATION_MINUTES:-60}
      - Jwt__RefreshTokenExpirationDays=${JWT_REFRESH_TOKEN_EXPIRATION_DAYS:-7}
      # Application Configuration
      - Application__DemoMode=${DEMO_MODE:-true}
      - Application__Environment=LocalDevelopment
      - Application__BaseUrl=${APP_BASE_URL:-https://localhost}
      # Data Seeding Configuration
      - DataSeeding__ForceReseed=${FORCE_RESEED:-false}
      - DataSeeding__Categories__Essential=true
      - DataSeeding__Categories__SampleData=true
      - DataSeeding__Categories__UserAccounts=true
      # Logging Configuration
      - Seq__ServerUrl=http://seq:80
      - Seq__ApiKey=${SEQ_API_KEY:-}
      # Feature Flags
      - Features__EnableSwagger=true
      - Features__EnableDetailedErrors=true
      - Features__EnableSensitiveDataLogging=false
    volumes:
      - uploads_data:/app/uploads
      - ./local-dev/logs:/app/logs
    networks:
      - harmoni360-frontend
      - harmoni360-backend
      - harmoni360-monitoring
    ports:
      - "${APP_PORT:-8080}:8080"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"

  # Nginx Reverse Proxy with SSL Termination
  nginx:
    image: nginx:alpine
    container_name: harmoni360-proxy-local
    hostname: nginx
    volumes:
      - ./local-dev/config/nginx/nginx.local.conf:/etc/nginx/nginx.conf:ro
      - ./local-dev/config/nginx/conf.d:/etc/nginx/conf.d:ro
      - ./local-dev/ssl:/etc/nginx/ssl:ro
      - uploads_data:/var/www/uploads:ro
      - ./local-dev/logs/nginx:/var/log/nginx
    networks:
      - harmoni360-frontend
    ports:
      - "${NGINX_HTTP_PORT:-80}:80"
      - "${NGINX_HTTPS_PORT:-443}:443"
    depends_on:
      app:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: harmoni360-prometheus-local
    hostname: prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
      - '--web.external-url=http://localhost:9090'
    volumes:
      - ./local-dev/config/prometheus/prometheus.local.yml:/etc/prometheus/prometheus.yml:ro
      - ./local-dev/config/prometheus/rules:/etc/prometheus/rules:ro
      - prometheus_data:/prometheus
    networks:
      - harmoni360-monitoring
      - harmoni360-backend
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 3G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"

  # Grafana Dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: harmoni360-grafana-local
    hostname: grafana
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SERVER_DOMAIN=localhost
      - GF_SERVER_ROOT_URL=http://localhost:3000
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource,grafana-piechart-panel
      - GF_SECURITY_ALLOW_EMBEDDING=true
      - GF_AUTH_ANONYMOUS_ENABLED=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./local-dev/config/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./local-dev/config/grafana/dashboards:/var/lib/grafana/dashboards:ro
    networks:
      - harmoni360-monitoring
    ports:
      - "${GRAFANA_PORT:-3000}:3000"
    depends_on:
      - prometheus
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"

  # Seq Structured Logging
  seq:
    image: datalust/seq:latest
    container_name: harmoni360-seq-local
    hostname: seq
    environment:
      - ACCEPT_EULA=Y
      - SEQ_FIRSTRUN_ADMINPASSWORDHASH=${SEQ_ADMIN_PASSWORD_HASH}
      - SEQ_CACHE_SYSTEMRAMTARGET=0.5
    volumes:
      - seq_data:/data
    networks:
      - harmoni360-monitoring
      - harmoni360-backend
    ports:
      - "${SEQ_PORT:-5341}:80"
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"

  # Node Exporter for System Metrics
  node-exporter:
    image: prom/node-exporter:latest
    container_name: harmoni360-node-exporter-local
    hostname: node-exporter
    command:
      - '--path.rootfs=/host'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    volumes:
      - '/:/host:ro,rslave'
    networks:
      - harmoni360-monitoring
    ports:
      - "${NODE_EXPORTER_PORT:-9100}:9100"
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'
    logging:
      driver: "json-file"
      options:
        max-size: "25m"
        max-file: "2"
