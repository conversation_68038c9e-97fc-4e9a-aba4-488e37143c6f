# Fly.io configuration file for Harmoni360 Production
# Production deployment configuration for harmoni-hse-360

app = "harmoni-hse-360"
primary_region = "sjc"

[build]
  dockerfile = "Dockerfile.flyio"

[env]
  ASPNETCORE_ENVIRONMENT = "Production"
  ASPNETCORE_URLS = "http://+:8080"
  ASPNETCORE_FORWARDEDHEADERS_ENABLED = "true"

[[services]]
  internal_port = 8080
  protocol = "tcp"
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 1

  [[services.ports]]
    handlers = ["http"]
    port = 80
    force_https = true

  [[services.ports]]
    handlers = ["tls", "http"]
    port = 443

  [services.concurrency]
    type = "connections"
    hard_limit = 50
    soft_limit = 40

[[services.http_checks]]
  interval = "10s"
  grace_period = "10s"
  method = "GET"
  path = "/health"
  protocol = "http"
  timeout = "5s"
  tls_skip_verify = false

[mounts]
  source = "harmoni360_uploads"
  destination = "/app/uploads"

# Production machine configuration
[vm]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 1024

# Restart policy for production stability
[restart]
  policy = "always"
